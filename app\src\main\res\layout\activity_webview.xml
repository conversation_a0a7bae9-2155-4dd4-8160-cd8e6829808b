<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout 
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".WebViewActivity">

    <!-- 顶部工具栏 -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/webview_toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary_blue"
        android:elevation="4dp"
        app:title="@string/webview_title"
        app:titleTextColor="@color/white"
        app:navigationIcon="@android:drawable/ic_menu_revert"
        app:navigationIconTint="@color/white"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- 控制按钮栏 -->
    <LinearLayout
        android:id="@+id/control_panel"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="8dp"
        android:background="@color/surface_light"
        android:elevation="2dp"
        app:layout_constraintTop_toBottomOf="@id/webview_toolbar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/refresh_button"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_weight="1"
            android:layout_marginEnd="4dp"
            android:text="@string/refresh_button"
            android:textSize="12sp"
            style="@style/ButtonSecondary"
            app:cornerRadius="20dp"
            app:icon="@android:drawable/ic_popup_sync"
            app:iconSize="16dp" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/auto_fill_button"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_weight="1"
            android:layout_marginStart="4dp"
            android:layout_marginEnd="4dp"
            android:text="@string/start_auto_fill"
            android:textSize="12sp"
            style="@style/ButtonPrimary"
            app:cornerRadius="20dp"
            app:icon="@android:drawable/ic_menu_edit"
            app:iconSize="16dp" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/auto_click_button"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_weight="1"
            android:layout_marginStart="4dp"
            android:text="@string/auto_click"
            android:textSize="12sp"
            style="@style/ButtonSecondary"
            app:cornerRadius="20dp"
            app:icon="@android:drawable/ic_menu_mylocation"
            app:iconSize="16dp" />

    </LinearLayout>

    <!-- WebView容器 -->
    <FrameLayout
        android:id="@+id/webview_container"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toBottomOf="@id/control_panel"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <!-- WebView -->
        <WebView
            android:id="@+id/webview"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <!-- 加载进度条 -->
        <com.google.android.material.progressindicator.LinearProgressIndicator
            android:id="@+id/progress_bar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="top"
            android:visibility="gone"
            app:indicatorColor="@color/primary_blue"
            app:trackColor="@color/background_light" />

        <!-- 加载状态覆盖层 -->
        <LinearLayout
            android:id="@+id/loading_overlay"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:gravity="center"
            android:background="@color/background_light"
            android:visibility="gone">

            <com.google.android.material.progressindicator.CircularProgressIndicator
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:indicatorColor="@color/primary_blue" />

            <TextView
                android:id="@+id/loading_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/loading"
                android:textSize="16sp"
                android:textColor="@color/text_secondary" />

        </LinearLayout>

    </FrameLayout>

    <!-- 浮动操作按钮 -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fab_stop"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:src="@android:drawable/ic_media_pause"
        android:contentDescription="@string/stop_automation"
        app:backgroundTint="@color/button_danger"
        app:tint="@color/white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:visibility="gone" />

</androidx.constraintlayout.widget.ConstraintLayout>
