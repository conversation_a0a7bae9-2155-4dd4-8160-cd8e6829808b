# Java环境自动配置脚本 (PowerShell版本)

Write-Host "================================" -ForegroundColor Green
Write-Host "Java环境自动配置脚本" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Green
Write-Host ""

# 检查Java是否已安装
Write-Host "正在检查Java安装状态..." -ForegroundColor Yellow
try {
    $javaVersion = java -version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Java已安装，版本信息：" -ForegroundColor Green
        java -version
        $javaInstalled = $true
    }
} catch {
    $javaInstalled = $false
}

if (-not $javaInstalled) {
    Write-Host "Java未安装，开始安装过程..." -ForegroundColor Red
    Write-Host ""
    
    # 检查Chocolatey
    Write-Host "正在检查Chocolatey..." -ForegroundColor Yellow
    try {
        choco --version | Out-Null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "使用Chocolatey安装OpenJDK 11..." -ForegroundColor Green
            choco install openjdk11 -y
            $javaInstalled = $true
        }
    } catch {
        Write-Host "Chocolatey未安装" -ForegroundColor Yellow
    }
    
    if (-not $javaInstalled) {
        Write-Host ""
        Write-Host "================================" -ForegroundColor Red
        Write-Host "手动安装Java步骤：" -ForegroundColor Red
        Write-Host "================================" -ForegroundColor Red
        Write-Host "1. 打开浏览器访问以下链接之一：" -ForegroundColor White
        Write-Host ""
        Write-Host "   Oracle JDK 11 (推荐):" -ForegroundColor Cyan
        Write-Host "   https://www.oracle.com/java/technologies/javase/jdk11-archive-downloads.html" -ForegroundColor Blue
        Write-Host ""
        Write-Host "   OpenJDK 11 (免费):" -ForegroundColor Cyan
        Write-Host "   https://adoptium.net/temurin/releases/?version=11" -ForegroundColor Blue
        Write-Host ""
        Write-Host "   Microsoft OpenJDK 11:" -ForegroundColor Cyan
        Write-Host "   https://docs.microsoft.com/en-us/java/openjdk/download" -ForegroundColor Blue
        Write-Host ""
        Write-Host "2. 下载适合Windows x64的JDK安装包" -ForegroundColor White
        Write-Host "3. 运行安装程序，记住安装路径" -ForegroundColor White
        Write-Host "4. 安装完成后重新运行此脚本" -ForegroundColor White
        Write-Host ""
        Read-Host "按Enter键继续..."
        return
    }
}

# 配置环境变量
Write-Host ""
Write-Host "================================" -ForegroundColor Green
Write-Host "配置环境变量" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Green

Write-Host "正在查找Java安装路径..." -ForegroundColor Yellow

$javaPaths = @(
    "C:\Program Files\Java\jdk*",
    "C:\Program Files\OpenJDK\jdk*",
    "C:\Program Files\Eclipse Adoptium\jdk*",
    "C:\Program Files\Microsoft\jdk*"
)

$javaPath = $null
foreach ($path in $javaPaths) {
    $found = Get-ChildItem -Path $path -Directory -ErrorAction SilentlyContinue | Select-Object -First 1
    if ($found) {
        $javaPath = $found.FullName
        break
    }
}

if (-not $javaPath) {
    Write-Host "未找到Java安装路径，请手动输入：" -ForegroundColor Red
    $javaPath = Read-Host "请输入Java安装路径（例如：C:\Program Files\Java\jdk-11.0.19）"
}

Write-Host "找到Java路径: $javaPath" -ForegroundColor Green
Write-Host ""

# 设置环境变量
Write-Host "设置环境变量..." -ForegroundColor Yellow
$env:JAVA_HOME = $javaPath
$env:PATH = "$javaPath\bin;$env:PATH"

# 验证配置
Write-Host "验证Java配置..." -ForegroundColor Yellow
try {
    java -version
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Java配置成功！" -ForegroundColor Green
    }
} catch {
    Write-Host "Java配置失败，请检查路径是否正确" -ForegroundColor Red
    return
}

# 设置永久环境变量
Write-Host ""
Write-Host "================================" -ForegroundColor Green
Write-Host "设置永久环境变量" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Green

try {
    [Environment]::SetEnvironmentVariable("JAVA_HOME", $javaPath, [EnvironmentVariableTarget]::User)
    
    $userPath = [Environment]::GetEnvironmentVariable("PATH", [EnvironmentVariableTarget]::User)
    if ($userPath -notlike "*$javaPath\bin*") {
        $newPath = "$userPath;$javaPath\bin"
        [Environment]::SetEnvironmentVariable("PATH", $newPath, [EnvironmentVariableTarget]::User)
        Write-Host "PATH已更新" -ForegroundColor Green
    } else {
        Write-Host "PATH已包含Java路径" -ForegroundColor Yellow
    }
    
    Write-Host "永久环境变量设置完成！" -ForegroundColor Green
} catch {
    Write-Host "设置永久环境变量时出错: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "================================" -ForegroundColor Green
Write-Host "配置完成！" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Green
Write-Host "Java版本:" -ForegroundColor Cyan
java -version
Write-Host ""
Write-Host "JAVA_HOME: $env:JAVA_HOME" -ForegroundColor Cyan
Write-Host ""
Write-Host "请重新打开PowerShell窗口，然后运行:" -ForegroundColor Yellow
Write-Host ".\gradlew clean" -ForegroundColor White
Write-Host ""

Read-Host "按Enter键退出..."
