package com.example.webautomation;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.view.View;
import android.webkit.WebChromeClient;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.LinearLayout;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;

import com.google.android.material.button.MaterialButton;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.google.android.material.progressindicator.LinearProgressIndicator;

/**
 * WebView活动 - 显示网页并提供自动化控制
 * 兼容Android 5.0+ (API 21+)
 */
public class WebViewActivity extends AppCompatActivity {
    
    public static final String EXTRA_URL = "extra_url";
    
    private WebView webView;
    private LinearProgressIndicator progressBar;
    private LinearLayout loadingOverlay;
    private MaterialButton refreshButton;
    private MaterialButton autoFillButton;
    private MaterialButton autoClickButton;
    private FloatingActionButton fabStop;
    private Toolbar toolbar;
    
    private String currentUrl;
    private boolean isAutomationRunning = false;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_webview);
        
        // 获取传入的URL
        currentUrl = getIntent().getStringExtra(EXTRA_URL);
        if (currentUrl == null) {
            currentUrl = "https://www.google.com";
        }
        
        initViews();
        setupWebView();
        setupListeners();
        loadUrl(currentUrl);
    }

    /**
     * 初始化视图组件
     */
    private void initViews() {
        toolbar = findViewById(R.id.webview_toolbar);
        webView = findViewById(R.id.webview);
        progressBar = findViewById(R.id.progress_bar);
        loadingOverlay = findViewById(R.id.loading_overlay);
        refreshButton = findViewById(R.id.refresh_button);
        autoFillButton = findViewById(R.id.auto_fill_button);
        autoClickButton = findViewById(R.id.auto_click_button);
        fabStop = findViewById(R.id.fab_stop);
        
        // 设置工具栏
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setDisplayShowHomeEnabled(true);
        }
    }

    /**
     * 配置WebView设置
     */
    @SuppressLint("SetJavaScriptEnabled")
    private void setupWebView() {
        WebSettings webSettings = webView.getSettings();
        
        // 启用JavaScript（自动化必需）
        webSettings.setJavaScriptEnabled(true);
        
        // 启用DOM存储
        webSettings.setDomStorageEnabled(true);
        
        // 启用数据库
        webSettings.setDatabaseEnabled(true);
        
        // 设置用户代理
        webSettings.setUserAgentString(webSettings.getUserAgentString() + " WebAutomationApp/1.0");
        
        // 支持缩放
        webSettings.setSupportZoom(true);
        webSettings.setBuiltInZoomControls(true);
        webSettings.setDisplayZoomControls(false);
        
        // 适应屏幕
        webSettings.setUseWideViewPort(true);
        webSettings.setLoadWithOverviewMode(true);
        
        // 兼容性设置
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP) {
            webSettings.setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);
        }
        
        // 设置WebViewClient
        webView.setWebViewClient(new CustomWebViewClient());
        
        // 设置WebChromeClient（用于进度条）
        webView.setWebChromeClient(new CustomWebChromeClient());
    }

    /**
     * 设置事件监听器
     */
    private void setupListeners() {
        // 返回按钮
        toolbar.setNavigationOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onBackPressed();
            }
        });
        
        // 刷新按钮
        refreshButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                refreshPage();
            }
        });
        
        // 自动填写按钮
        autoFillButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                startAutoFill();
            }
        });
        
        // 自动点击按钮
        autoClickButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                startAutoClick();
            }
        });
        
        // 停止自动化按钮
        fabStop.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                stopAutomation();
            }
        });
    }

    /**
     * 加载URL
     */
    private void loadUrl(String url) {
        showLoading(true);
        webView.loadUrl(url);
    }

    /**
     * 刷新页面
     */
    private void refreshPage() {
        webView.reload();
    }

    /**
     * 开始自动填写（占位符方法）
     */
    private void startAutoFill() {
        if (isAutomationRunning) {
            Toast.makeText(this, "自动化正在运行中", Toast.LENGTH_SHORT).show();
            return;
        }
        
        isAutomationRunning = true;
        fabStop.setVisibility(View.VISIBLE);
        
        // TODO: 实现自动填写逻辑
        Toast.makeText(this, getString(R.string.automation_started), Toast.LENGTH_SHORT).show();
        
        // 示例：注入JavaScript来填写表单
        String javascript = "javascript:(function(){" +
                "var inputs = document.getElementsByTagName('input');" +
                "for(var i=0; i<inputs.length; i++){" +
                "if(inputs[i].type=='text' || inputs[i].type=='email'){" +
                "inputs[i].value='示例文本';" +
                "}" +
                "}" +
                "})()";
        
        webView.evaluateJavascript(javascript, null);
    }

    /**
     * 开始自动点击（占位符方法）
     */
    private void startAutoClick() {
        if (isAutomationRunning) {
            Toast.makeText(this, "自动化正在运行中", Toast.LENGTH_SHORT).show();
            return;
        }
        
        isAutomationRunning = true;
        fabStop.setVisibility(View.VISIBLE);
        
        // TODO: 实现自动点击逻辑
        Toast.makeText(this, "自动点击功能启动", Toast.LENGTH_SHORT).show();
    }

    /**
     * 停止自动化
     */
    private void stopAutomation() {
        isAutomationRunning = false;
        fabStop.setVisibility(View.GONE);
        Toast.makeText(this, getString(R.string.automation_stopped), Toast.LENGTH_SHORT).show();
    }

    /**
     * 显示/隐藏加载状态
     */
    private void showLoading(boolean show) {
        if (show) {
            loadingOverlay.setVisibility(View.VISIBLE);
            progressBar.setVisibility(View.VISIBLE);
        } else {
            loadingOverlay.setVisibility(View.GONE);
            progressBar.setVisibility(View.GONE);
        }
    }

    /**
     * 自定义WebViewClient
     */
    private class CustomWebViewClient extends WebViewClient {
        @Override
        public void onPageStarted(WebView view, String url, android.graphics.Bitmap favicon) {
            super.onPageStarted(view, url, favicon);
            showLoading(true);
        }

        @Override
        public void onPageFinished(WebView view, String url) {
            super.onPageFinished(view, url);
            showLoading(false);
            Toast.makeText(WebViewActivity.this, getString(R.string.page_loaded), Toast.LENGTH_SHORT).show();
        }

        @Override
        public void onReceivedError(WebView view, int errorCode, String description, String failingUrl) {
            super.onReceivedError(view, errorCode, description, failingUrl);
            showLoading(false);
            Toast.makeText(WebViewActivity.this, getString(R.string.error_page_load), Toast.LENGTH_LONG).show();
        }
    }

    /**
     * 自定义WebChromeClient
     */
    private class CustomWebChromeClient extends WebChromeClient {
        @Override
        public void onProgressChanged(WebView view, int newProgress) {
            super.onProgressChanged(view, newProgress);
            if (newProgress < 100) {
                progressBar.setVisibility(View.VISIBLE);
                progressBar.setProgress(newProgress);
            } else {
                progressBar.setVisibility(View.GONE);
            }
        }
    }

    @Override
    public void onBackPressed() {
        if (webView.canGoBack()) {
            webView.goBack();
        } else {
            super.onBackPressed();
        }
    }

    @Override
    protected void onDestroy() {
        if (webView != null) {
            webView.destroy();
        }
        super.onDestroy();
    }
}
