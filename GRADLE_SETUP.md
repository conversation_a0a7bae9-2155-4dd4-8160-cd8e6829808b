# 🔧 Gradle JVM 兼容性问题解决方案

## 问题描述
```
Gradle JVM version incompatible.
This project is configured to use an older Gradle JVM that supports up to version 8 but the current
```

## 解决方案

### 方案1：在Android Studio中设置JDK版本 (推荐)

1. **打开项目设置**
   - Windows: `File` → `Settings`
   - Mac: `Android Studio` → `Preferences`

2. **修改Gradle JDK**
   - 导航到: `Build, Execution, Deployment` → `Build Tools` → `Gradle`
   - 在 `Gradle JDK` 下拉菜单中选择:
     - `JDK 11` (推荐)
     - `JDK 17` (如果可用)
   - 点击 `Apply` 和 `OK`

3. **同步项目**
   - 点击 `Sync Now` 或 `File` → `Sync Project with Gradle Files`

### 方案2：检查Java版本

在命令行中检查Java版本：
```bash
java -version
javac -version
```

确保使用Java 11或更高版本。

### 方案3：设置JAVA_HOME环境变量

#### Windows:
```batch
set JAVA_HOME=C:\Program Files\Java\jdk-11.0.x
set PATH=%JAVA_HOME%\bin;%PATH%
```

#### Mac/Linux:
```bash
export JAVA_HOME=/usr/lib/jvm/java-11-openjdk
export PATH=$JAVA_HOME/bin:$PATH
```

### 方案4：使用项目本地JDK设置

在项目根目录创建 `local.properties` 文件：
```properties
sdk.dir=C\:\\Users\\YourName\\AppData\\Local\\Android\\Sdk
java.home=C\:\\Program Files\\Java\\jdk-11.0.x
```

## 版本兼容性表

| Gradle版本 | 最低Java版本 | 推荐Java版本 |
|-----------|-------------|-------------|
| 7.0-7.6   | Java 8      | Java 11     |
| 8.0-8.4   | Java 8      | Java 17     |
| 8.5+      | Java 17     | Java 17     |

## 当前项目配置

- **Gradle版本**: 7.5
- **Android Gradle Plugin**: 7.4.2
- **推荐Java版本**: Java 11
- **最低Java版本**: Java 8

## 验证步骤

1. **检查Gradle版本**
   ```bash
   ./gradlew --version
   ```

2. **清理并重新构建**
   ```bash
   ./gradlew clean
   ./gradlew build
   ```

3. **如果仍有问题，尝试**
   ```bash
   ./gradlew --stop
   ./gradlew clean build
   ```

## 常见错误和解决方案

### 错误1: "Could not determine java version"
**解决**: 确保JAVA_HOME指向正确的JDK目录

### 错误2: "Unsupported class file major version"
**解决**: 降低Java版本或升级Gradle版本

### 错误3: "Could not find or load main class"
**解决**: 检查PATH环境变量是否包含Java bin目录

## 快速修复脚本

创建 `fix-gradle.bat` (Windows):
```batch
@echo off
echo 正在修复Gradle配置...
set JAVA_HOME=C:\Program Files\Java\jdk-11.0.19
gradlew --stop
gradlew clean
echo 修复完成，请重新打开Android Studio
pause
```

## 联系支持

如果问题仍然存在，请提供以下信息：
- Java版本 (`java -version`)
- Gradle版本 (`./gradlew --version`)
- Android Studio版本
- 操作系统版本
- 完整的错误日志
