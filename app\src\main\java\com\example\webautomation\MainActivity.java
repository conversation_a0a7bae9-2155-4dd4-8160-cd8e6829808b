package com.example.webautomation;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Patterns;
import android.view.View;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.core.content.ContextCompat;

import com.google.android.material.button.MaterialButton;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;

/**
 * 主活动 - 用户输入URL并启动自动化
 * 兼容Android 5.0+ (API 21+)
 */
public class MainActivity extends AppCompatActivity {
    
    private TextInputLayout urlInputLayout;
    private TextInputEditText urlEditText;
    private MaterialButton startAutomationButton;
    
    // 预设的常用网址
    private static final String[] COMMON_URLS = {
        "https://www.google.com",
        "https://www.baidu.com",
        "https://github.com",
        "https://stackoverflow.com"
    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);
        
        initViews();
        setupListeners();
        setupUI();
    }

    /**
     * 初始化视图组件
     */
    private void initViews() {
        urlInputLayout = findViewById(R.id.url_input_layout);
        urlEditText = findViewById(R.id.url_edit_text);
        startAutomationButton = findViewById(R.id.start_automation_button);
    }

    /**
     * 设置事件监听器
     */
    private void setupListeners() {
        startAutomationButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                startWebAutomation();
            }
        });
        
        // URL输入框文本变化监听
        urlEditText.setOnFocusChangeListener(new View.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                if (!hasFocus) {
                    validateUrl();
                }
            }
        });
    }

    /**
     * 设置UI样式和兼容性
     */
    private void setupUI() {
        // 设置状态栏颜色（API 21+）
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP) {
            getWindow().setStatusBarColor(ContextCompat.getColor(this, R.color.primary_blue_dark));
        }
        
        // 预填充示例URL
        urlEditText.setText("https://www.example.com");
    }

    /**
     * 验证URL格式
     */
    private boolean validateUrl() {
        String url = urlEditText.getText().toString().trim();
        
        if (TextUtils.isEmpty(url)) {
            urlInputLayout.setError(getString(R.string.error_invalid_url));
            return false;
        }
        
        // 自动添加协议前缀
        if (!url.startsWith("http://") && !url.startsWith("https://")) {
            url = "https://" + url;
            urlEditText.setText(url);
        }
        
        // 验证URL格式
        if (!Patterns.WEB_URL.matcher(url).matches()) {
            urlInputLayout.setError(getString(R.string.error_invalid_url));
            return false;
        }
        
        urlInputLayout.setError(null);
        return true;
    }

    /**
     * 启动Web自动化
     */
    private void startWebAutomation() {
        if (!validateUrl()) {
            return;
        }
        
        String url = urlEditText.getText().toString().trim();
        
        // 显示加载状态
        startAutomationButton.setEnabled(false);
        startAutomationButton.setText(getString(R.string.loading));
        
        try {
            // 启动WebView活动
            Intent intent = new Intent(this, WebViewActivity.class);
            intent.putExtra(WebViewActivity.EXTRA_URL, url);
            startActivity(intent);
            
            // 显示成功提示
            Toast.makeText(this, getString(R.string.automation_started), Toast.LENGTH_SHORT).show();
            
        } catch (Exception e) {
            // 错误处理
            Toast.makeText(this, getString(R.string.error_automation), Toast.LENGTH_LONG).show();
            e.printStackTrace();
        } finally {
            // 恢复按钮状态
            startAutomationButton.setEnabled(true);
            startAutomationButton.setText(getString(R.string.start_automation));
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        // 重置按钮状态
        startAutomationButton.setEnabled(true);
        startAutomationButton.setText(getString(R.string.start_automation));
    }

    /**
     * 处理返回键
     */
    @Override
    public void onBackPressed() {
        // 清除输入框错误状态
        if (urlInputLayout.getError() != null) {
            urlInputLayout.setError(null);
        } else {
            super.onBackPressed();
        }
    }
}
