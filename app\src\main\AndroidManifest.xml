<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- 网络访问权限 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    
    <!-- 允许HTTP流量（用于测试，生产环境建议移除） -->
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.WebAutomation"
        android:usesCleartextTraffic="true"
        tools:targetApi="31">
        
        <!-- 主活动 -->
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:theme="@style/Theme.WebAutomation">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        
        <!-- WebView活动 -->
        <activity
            android:name=".WebViewActivity"
            android:exported="false"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:theme="@style/Theme.WebAutomation" />
            
    </application>

</manifest>
