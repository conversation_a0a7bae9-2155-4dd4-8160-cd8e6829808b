@echo off
echo ================================
echo 快速修复Java环境问题
echo ================================
echo.

echo 步骤1：检查常见Java安装位置...
set JAVA_FOUND=0

if exist "C:\Program Files\Java\jdk-11*" (
    for /d %%i in ("C:\Program Files\Java\jdk-11*") do (
        set JAVA_HOME=%%i
        set JAVA_FOUND=1
        goto :found
    )
)

if exist "C:\Program Files\Java\jdk-17*" (
    for /d %%i in ("C:\Program Files\Java\jdk-17*") do (
        set JAVA_HOME=%%i
        set JAVA_FOUND=1
        goto :found
    )
)

if exist "C:\Program Files\OpenJDK\jdk*" (
    for /d %%i in ("C:\Program Files\OpenJDK\jdk*") do (
        set JAVA_HOME=%%i
        set JAVA_FOUND=1
        goto :found
    )
)

if exist "C:\Program Files\Eclipse Adoptium\jdk*" (
    for /d %%i in ("C:\Program Files\Eclipse Adoptium\jdk*") do (
        set JAVA_HOME=%%i
        set JAVA_FOUND=1
        goto :found
    )
)

:found
if %JAVA_FOUND%==1 (
    echo 找到Java安装: %JAVA_HOME%
    set PATH=%JAVA_HOME%\bin;%PATH%
    echo.
    echo 验证Java版本:
    "%JAVA_HOME%\bin\java" -version
    echo.
    echo 现在可以运行Gradle命令了:
    echo .\gradlew clean
    echo .\gradlew build
) else (
    echo 未找到Java安装，请先安装Java JDK 11或更高版本
    echo.
    echo 推荐下载链接:
    echo https://adoptium.net/temurin/releases/?version=11
    echo.
    echo 或运行: setup-java.bat
)

echo.
pause
