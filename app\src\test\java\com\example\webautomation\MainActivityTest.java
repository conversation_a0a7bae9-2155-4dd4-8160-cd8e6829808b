package com.example.webautomation;

import org.junit.Test;
import static org.junit.Assert.*;

/**
 * 主活动单元测试
 * 
 * 测试URL验证和基本功能
 */
public class MainActivityTest {
    
    @Test
    public void testUrlValidation() {
        // 测试有效URL
        assertTrue("有效的HTTPS URL应该通过验证", isValidUrl("https://www.google.com"));
        assertTrue("有效的HTTP URL应该通过验证", isValidUrl("http://www.example.com"));
        
        // 测试无效URL
        assertFalse("空字符串应该验证失败", isValidUrl(""));
        assertFalse("无效格式应该验证失败", isValidUrl("not-a-url"));
        assertFalse("null值应该验证失败", isValidUrl(null));
    }
    
    @Test
    public void testUrlFormatting() {
        // 测试自动添加协议
        assertEquals("应该自动添加https前缀", "https://www.google.com", formatUrl("www.google.com"));
        assertEquals("已有协议的URL不应该修改", "http://www.example.com", formatUrl("http://www.example.com"));
    }
    
    // 模拟URL验证逻辑（实际实现在MainActivity中）
    private boolean isValidUrl(String url) {
        if (url == null || url.trim().isEmpty()) {
            return false;
        }
        
        String formattedUrl = formatUrl(url);
        return android.util.Patterns.WEB_URL.matcher(formattedUrl).matches();
    }
    
    // 模拟URL格式化逻辑
    private String formatUrl(String url) {
        if (url == null) return "";
        
        String trimmed = url.trim();
        if (!trimmed.startsWith("http://") && !trimmed.startsWith("https://")) {
            return "https://" + trimmed;
        }
        return trimmed;
    }
}
