@echo off
echo ================================
echo Java环境自动配置脚本
echo ================================
echo.

echo 正在检查Java安装状态...
java -version >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo Java已安装，版本信息：
    java -version
    goto :configure_env
)

echo Java未安装，开始安装过程...
echo.

echo 方案1：使用Chocolatey安装Java 11
echo 正在检查Chocolatey...
choco --version >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo 使用Chocolatey安装OpenJDK 11...
    choco install openjdk11 -y
    goto :configure_env
)

echo.
echo Chocolatey未安装，将使用手动安装方式
echo.
echo ================================
echo 手动安装Java步骤：
echo ================================
echo 1. 打开浏览器访问以下链接之一：
echo.
echo    Oracle JDK 11 (推荐):
echo    https://www.oracle.com/java/technologies/javase/jdk11-archive-downloads.html
echo.
echo    OpenJDK 11 (免费):
echo    https://adoptium.net/temurin/releases/?version=11
echo.
echo    Microsoft OpenJDK 11:
echo    https://docs.microsoft.com/en-us/java/openjdk/download
echo.
echo 2. 下载适合Windows x64的JDK安装包
echo 3. 运行安装程序，记住安装路径（通常是 C:\Program Files\Java\jdk-11.x.x）
echo 4. 安装完成后重新运行此脚本
echo.
pause
goto :end

:configure_env
echo.
echo ================================
echo 配置环境变量
echo ================================

echo 正在查找Java安装路径...
for /d %%i in ("C:\Program Files\Java\jdk*") do set JAVA_PATH=%%i
for /d %%i in ("C:\Program Files\OpenJDK\jdk*") do set JAVA_PATH=%%i
for /d %%i in ("C:\Program Files\Eclipse Adoptium\jdk*") do set JAVA_PATH=%%i
for /d %%i in ("C:\Program Files\Microsoft\jdk*") do set JAVA_PATH=%%i

if not defined JAVA_PATH (
    echo 未找到Java安装路径，请手动设置：
    echo.
    set /p JAVA_PATH=请输入Java安装路径（例如：C:\Program Files\Java\jdk-11.0.19）: 
)

echo 找到Java路径: %JAVA_PATH%
echo.

echo 设置当前会话的环境变量...
set JAVA_HOME=%JAVA_PATH%
set PATH=%JAVA_HOME%\bin;%PATH%

echo 验证Java配置...
java -version
if %ERRORLEVEL% NEQ 0 (
    echo Java配置失败，请检查路径是否正确
    goto :end
)

echo.
echo ================================
echo 设置永久环境变量
echo ================================
echo 正在设置系统环境变量...

setx JAVA_HOME "%JAVA_PATH%" /M >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo 需要管理员权限设置系统环境变量
    echo 正在设置用户环境变量...
    setx JAVA_HOME "%JAVA_PATH%"
)

echo JAVA_HOME已设置为: %JAVA_PATH%
echo.

echo 更新PATH环境变量...
for /f "tokens=2*" %%a in ('reg query "HKCU\Environment" /v PATH 2^>nul') do set USER_PATH=%%b
if not defined USER_PATH set USER_PATH=
echo %USER_PATH% | find "%JAVA_PATH%\bin" >nul
if %ERRORLEVEL% NEQ 0 (
    setx PATH "%USER_PATH%;%JAVA_PATH%\bin"
    echo PATH已更新
) else (
    echo PATH已包含Java路径
)

echo.
echo ================================
echo 配置完成！
echo ================================
echo Java版本:
java -version
echo.
echo JAVA_HOME: %JAVA_HOME%
echo.
echo 请重新打开命令提示符或PowerShell窗口
echo 然后运行: .\gradlew clean
echo.

:end
pause
