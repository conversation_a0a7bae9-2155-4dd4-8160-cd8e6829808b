@echo off
echo ================================
echo Web自动化助手 - Android应用构建
echo ================================
echo.

echo 正在检查环境...
if not exist "gradlew.bat" (
    echo 错误: 找不到gradlew.bat文件
    echo 请确保在项目根目录运行此脚本
    pause
    exit /b 1
)

echo 正在清理项目...
call gradlew clean

echo.
echo 正在构建Debug版本...
call gradlew assembleDebug

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ================================
    echo 构建成功！
    echo ================================
    echo APK文件位置: app\build\outputs\apk\debug\app-debug.apk
    echo.
    echo 您可以将此APK文件安装到Android设备上进行测试
    echo.
) else (
    echo.
    echo ================================
    echo 构建失败！
    echo ================================
    echo 请检查错误信息并修复问题后重试
    echo.
)

pause
