<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout 
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_light"
    tools:context=".MainActivity">

    <!-- 顶部标题栏 -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary_blue"
        android:elevation="4dp"
        app:title="@string/main_title"
        app:titleTextColor="@color/white"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- 主要内容区域 -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:fillViewport="true"
        app:layout_constraintTop_toBottomOf="@id/toolbar"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp"
            android:gravity="center_horizontal">

            <!-- Logo或图标区域 -->
            <ImageView
                android:id="@+id/app_logo"
                android:layout_width="120dp"
                android:layout_height="120dp"
                android:layout_marginTop="32dp"
                android:layout_marginBottom="32dp"
                android:src="@mipmap/ic_launcher"
                android:contentDescription="@string/app_name" />

            <!-- URL输入卡片 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/surface_light">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/url_label"
                        android:textSize="16sp"
                        android:textColor="@color/text_primary"
                        android:textStyle="bold"
                        android:layout_marginBottom="12dp" />

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/url_input_layout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/url_hint"
                        app:boxStrokeColor="@color/primary_blue"
                        app:hintTextColor="@color/text_hint"
                        style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/url_edit_text"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textUri"
                            android:maxLines="1"
                            android:textSize="14sp" />

                    </com.google.android.material.textfield.TextInputLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 操作按钮 -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/start_automation_button"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:text="@string/start_automation"
                android:textSize="16sp"
                android:textStyle="bold"
                style="@style/ButtonPrimary"
                app:cornerRadius="28dp"
                app:icon="@android:drawable/ic_media_play"
                app:iconGravity="textStart"
                app:iconPadding="8dp" />

            <!-- 状态显示 -->
            <TextView
                android:id="@+id/status_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text=""
                android:textSize="14sp"
                android:textColor="@color/text_secondary"
                android:visibility="gone" />

        </LinearLayout>

    </ScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>
