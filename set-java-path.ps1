# 设置Java环境变量 (PowerShell版本)

Write-Host "================================" -ForegroundColor Green
Write-Host "设置Java环境变量" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Green
Write-Host ""

$javaHome = "C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot"

Write-Host "设置JAVA_HOME为: $javaHome" -ForegroundColor Yellow
Write-Host ""

# 验证Java路径
Write-Host "验证Java路径是否存在..." -ForegroundColor Yellow
$javaExe = Join-Path $javaHome "bin\java.exe"
if (-not (Test-Path $javaExe)) {
    Write-Host "错误: 在指定路径未找到java.exe" -ForegroundColor Red
    Write-Host "请检查路径是否正确: $javaHome" -ForegroundColor Red
    Read-Host "按Enter键退出..."
    exit 1
}

Write-Host "Java路径验证成功！" -ForegroundColor Green
Write-Host ""

# 设置当前会话环境变量
Write-Host "设置当前会话环境变量..." -ForegroundColor Yellow
$env:JAVA_HOME = $javaHome
$env:PATH = "$javaHome\bin;$env:PATH"

# 测试Java命令
Write-Host "测试Java命令..." -ForegroundColor Yellow
try {
    & "$javaHome\bin\java.exe" -version
    Write-Host ""
    Write-Host "Java命令测试成功！" -ForegroundColor Green
} catch {
    Write-Host "Java命令测试失败: $($_.Exception.Message)" -ForegroundColor Red
    Read-Host "按Enter键退出..."
    exit 1
}

Write-Host ""
Write-Host "================================" -ForegroundColor Green
Write-Host "设置永久环境变量" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Green

try {
    # 设置用户级JAVA_HOME环境变量
    Write-Host "设置用户级JAVA_HOME环境变量..." -ForegroundColor Yellow
    [Environment]::SetEnvironmentVariable("JAVA_HOME", $javaHome, [EnvironmentVariableTarget]::User)
    
    # 获取当前用户PATH
    Write-Host "获取当前用户PATH..." -ForegroundColor Yellow
    $userPath = [Environment]::GetEnvironmentVariable("PATH", [EnvironmentVariableTarget]::User)
    if (-not $userPath) { $userPath = "" }
    
    # 检查PATH是否已包含Java路径
    $javaBinPath = "$javaHome\bin"
    if ($userPath -notlike "*$javaBinPath*") {
        Write-Host "添加Java路径到用户PATH..." -ForegroundColor Yellow
        if ($userPath) {
            $newPath = "$userPath;$javaBinPath"
        } else {
            $newPath = $javaBinPath
        }
        [Environment]::SetEnvironmentVariable("PATH", $newPath, [EnvironmentVariableTarget]::User)
        Write-Host "PATH已更新" -ForegroundColor Green
    } else {
        Write-Host "PATH已包含Java路径" -ForegroundColor Yellow
    }
    
    Write-Host "永久环境变量设置成功！" -ForegroundColor Green
    
} catch {
    Write-Host "设置永久环境变量时出错: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "您可能需要手动设置环境变量" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "================================" -ForegroundColor Green
Write-Host "配置完成！" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Green
Write-Host "JAVA_HOME: $env:JAVA_HOME" -ForegroundColor Cyan
Write-Host ""
Write-Host "当前会话中Java已可用:" -ForegroundColor Green
java -version
Write-Host ""
Write-Host "重要提示:" -ForegroundColor Yellow
Write-Host "1. 请关闭当前PowerShell窗口" -ForegroundColor White
Write-Host "2. 重新打开新的PowerShell窗口" -ForegroundColor White
Write-Host "3. 运行: java -version 验证配置" -ForegroundColor White
Write-Host "4. 然后运行: .\gradlew clean" -ForegroundColor White
Write-Host ""

Read-Host "按Enter键退出..."
