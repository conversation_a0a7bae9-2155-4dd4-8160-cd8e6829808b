# Web自动化助手 Android应用

一个用于访问固定网址并进行自动化网页操作的Android应用程序。

## 功能特性

- 🌐 **网页访问**: 支持访问任意网址
- 🤖 **自动化操作**: 支持表单自动填写和按钮自动点击
- 📱 **兼容性**: 支持Android 5.0+ (API 21+)，覆盖95%+的设备
- 🎨 **现代UI**: 采用Material Design 3设计规范
- 🔒 **安全性**: 支持HTTPS，包含网络安全配置

## 技术架构

### 开发环境
- **IDE**: Android Studio
- **语言**: Java
- **最低SDK**: API 21 (Android 5.0)
- **目标SDK**: API 34 (Android 14)
- **构建工具**: Gradle 8.1.4

### 主要依赖
- `androidx.appcompat:appcompat:1.6.1` - 向后兼容支持
- `com.google.android.material:material:1.10.0` - Material Design组件
- `androidx.webkit:webkit:1.8.0` - 现代WebView支持
- `androidx.constraintlayout:constraintlayout:2.1.4` - 约束布局

### 架构模式
- **MVVM**: Model-View-ViewModel架构
- **组件化**: 模块化设计，便于维护和扩展

## 项目结构

```
app/
├── src/main/
│   ├── java/com/example/webautomation/
│   │   ├── MainActivity.java          # 主活动
│   │   └── WebViewActivity.java       # WebView活动
│   ├── res/
│   │   ├── layout/
│   │   │   ├── activity_main.xml      # 主界面布局
│   │   │   └── activity_webview.xml   # WebView界面布局
│   │   ├── values/
│   │   │   ├── strings.xml            # 字符串资源
│   │   │   ├── colors.xml             # 颜色资源
│   │   │   └── themes.xml             # 主题配置
│   │   └── xml/
│   │       ├── backup_rules.xml       # 备份规则
│   │       └── data_extraction_rules.xml
│   └── AndroidManifest.xml            # 应用清单
├── build.gradle                       # 模块级构建配置
└── proguard-rules.pro                 # 代码混淆规则
```

## 兼容性说明

### Android版本支持
- **最低版本**: Android 5.0 (API 21) - 2014年发布
- **目标版本**: Android 14 (API 34) - 2023年发布
- **覆盖率**: 支持95%+的Android设备

### WebView兼容性
- 使用`androidx.webkit`库确保现代WebView功能
- 支持JavaScript注入和DOM操作
- 兼容HTTP和HTTPS协议
- 支持混合内容模式

### UI兼容性
- Material Design 3组件向下兼容
- 响应式布局适配不同屏幕尺寸
- 支持深色模式（系统级别）

## 安装和运行

### 环境要求
1. Android Studio Arctic Fox或更高版本
2. JDK 8或更高版本
3. Android SDK API 21-34

### 构建步骤
1. 克隆或下载项目到本地
2. 使用Android Studio打开项目
3. 等待Gradle同步完成
4. 连接Android设备或启动模拟器
5. 点击"Run"按钮构建并安装应用

### 测试设备
- **推荐**: Android 7.0+ (API 24+)
- **最低**: Android 5.0+ (API 21+)
- **RAM**: 建议2GB以上

## 使用说明

### 主界面
1. 在URL输入框中输入目标网址
2. 应用会自动验证URL格式并添加协议前缀
3. 点击"开始自动化"按钮启动WebView

### WebView界面
1. **刷新**: 重新加载当前页面
2. **自动填写**: 自动填写页面中的表单字段
3. **自动点击**: 自动点击页面中的按钮或链接
4. **停止**: 停止当前的自动化操作

## 开发计划

### 当前状态 ✅
- [x] 基础UI界面设计
- [x] WebView集成和配置
- [x] 基本的网页加载功能
- [x] 兼容性配置

### 下一步开发 🚧
- [ ] JavaScript注入自动化逻辑
- [ ] 表单识别和自动填写
- [ ] 按钮识别和自动点击
- [ ] 配置文件管理
- [ ] 操作记录和回放

### 未来功能 📋
- [ ] 多网站配置管理
- [ ] 自定义自动化脚本
- [ ] 操作日志和统计
- [ ] 云端配置同步

## 注意事项

### 权限说明
- `INTERNET`: 网络访问权限
- `ACCESS_NETWORK_STATE`: 网络状态检查
- `ACCESS_WIFI_STATE`: WiFi状态检查

### 安全考虑
- 支持HTTPS加密连接
- 启用了混合内容模式（开发阶段）
- 生产环境建议禁用HTTP明文传输

### 性能优化
- WebView硬件加速
- 图片和资源缓存
- 内存管理和回收

## 贡献指南

欢迎提交Issue和Pull Request来改进这个项目！

## 许可证

本项目采用MIT许可证 - 详见LICENSE文件
