<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme -->
    <style name="Theme.WebAutomation" parent="Theme.Material3.DayNight">
        <!-- Primary brand color -->
        <item name="colorPrimary">@color/primary_blue</item>
        <item name="colorPrimaryVariant">@color/primary_blue_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <!-- Secondary brand color -->
        <item name="colorSecondary">@color/accent_orange</item>
        <item name="colorSecondaryVariant">@color/accent_orange</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <!-- Status bar color -->
        <item name="android:statusBarColor" tools:targetApi="l">?attr/colorPrimaryVariant</item>
        
        <!-- Background colors -->
        <item name="android:colorBackground">@color/background_light</item>
        <item name="colorSurface">@color/surface_light</item>
        <item name="colorOnBackground">@color/text_primary</item>
        <item name="colorOnSurface">@color/text_primary</item>
        
        <!-- Error color -->
        <item name="colorError">@color/error_red</item>
        <item name="colorOnError">@color/white</item>
        
        <!-- 确保兼容性 -->
        <item name="android:windowContentTransitions">true</item>
        <item name="android:windowAllowEnterTransitionOverlap">true</item>
        <item name="android:windowAllowReturnTransitionOverlap">true</item>
    </style>
    
    <!-- WebView全屏主题 -->
    <style name="Theme.WebAutomation.WebView" parent="Theme.WebAutomation">
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>
    
    <!-- 按钮样式 -->
    <style name="ButtonPrimary" parent="Widget.Material3.Button">
        <item name="backgroundTint">@color/button_primary</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">16sp</item>
        <item name="android:padding">12dp</item>
    </style>
    
    <style name="ButtonSecondary" parent="Widget.Material3.Button.OutlinedButton">
        <item name="strokeColor">@color/button_primary</item>
        <item name="android:textColor">@color/button_primary</item>
        <item name="android:textSize">16sp</item>
        <item name="android:padding">12dp</item>
    </style>
</resources>
