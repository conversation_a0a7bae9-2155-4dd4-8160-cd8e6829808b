@echo off
echo ================================
echo 设置Java环境变量
echo ================================
echo.

set JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot

echo 设置JAVA_HOME为: %JAVA_HOME%
echo.

echo 验证Java路径是否存在...
if not exist "%JAVA_HOME%\bin\java.exe" (
    echo 错误: 在指定路径未找到java.exe
    echo 请检查路径是否正确: %JAVA_HOME%
    pause
    exit /b 1
)

echo Java路径验证成功！
echo.

echo 设置当前会话环境变量...
set PATH=%JAVA_HOME%\bin;%PATH%

echo 测试Java命令...
"%JAVA_HOME%\bin\java" -version
echo.

echo ================================
echo 设置永久环境变量
echo ================================

echo 设置用户级JAVA_HOME环境变量...
setx JAVA_HOME "%JAVA_HOME%"

echo 获取当前用户PATH...
for /f "tokens=2*" %%a in ('reg query "HKCU\Environment" /v PATH 2^>nul') do set USER_PATH=%%b
if not defined USER_PATH set USER_PATH=

echo 检查PATH是否已包含Java路径...
echo %USER_PATH% | find "%JAVA_HOME%\bin" >nul
if %ERRORLEVEL% NEQ 0 (
    echo 添加Java路径到用户PATH...
    if defined USER_PATH (
        setx PATH "%USER_PATH%;%JAVA_HOME%\bin"
    ) else (
        setx PATH "%JAVA_HOME%\bin"
    )
    echo PATH已更新
) else (
    echo PATH已包含Java路径
)

echo.
echo ================================
echo 配置完成！
echo ================================
echo JAVA_HOME: %JAVA_HOME%
echo.
echo 当前会话中Java已可用:
java -version
echo.
echo 重要提示:
echo 1. 请关闭当前PowerShell/命令提示符窗口
echo 2. 重新打开新的PowerShell窗口
echo 3. 运行: java -version 验证配置
echo 4. 然后运行: .\gradlew clean
echo.
pause
